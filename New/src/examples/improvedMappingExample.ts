/**
 * Example: Improved mapCCFieldToAPType API Usage
 * 
 * This example demonstrates the cleaner API after removing redundant parameters.
 */

import { mapCCFieldToAPType, type EnhancedFieldMappingResult } from "../processors/customFields/mapping";
import type { GetCCCustomField } from "@/type";

/**
 * Example showing the improved API usage patterns
 */
export function demonstrateImprovedAPI() {
	console.log("🚀 Demonstrating improved mapCCFieldToAPType API...");

	// Sample CC field data
	const ccField: GetCCCustomField = {
		id: 1,
		name: "phone",
		label: "Phone Number",
		type: "telephone",
		allowMultipleValues: false,
		validation: "",
		color: null,
		positions: [],
		useCustomSort: null,
		isRequired: false,
		allowedValues: [],
		defaultValues: []
	};

	// ✅ NEW: Simplified usage - auto-extracts field name/label, defaults to CC→AP
	console.log("\n📱 Simplified enhanced mapping (RECOMMENDED):");
	const result1 = mapCCFieldToAPType(ccField, {}) as EnhancedFieldMappingResult;
	console.log({
		mappingType: result1.mappingType,
		confidence: result1.confidence,
		fieldName: result1.fieldName,
		fieldLabel: result1.fieldLabel
	});

	// ✅ Basic usage still works (backward compatible)
	console.log("\n📱 Basic field type mapping (backward compatible):");
	const basicResult = mapCCFieldToAPType(ccField);
	console.log(`Field type: ${basicResult}`);

	// ✅ Advanced usage with overrides
	console.log("\n📱 Advanced usage with overrides:");
	const result2 = mapCCFieldToAPType(ccField, {
		fieldLabel: "Mobile Phone",  // Override label for fuzzy matching
		sourcePlatform: "ap",        // Reverse direction
		targetPlatform: "cc"
	}) as EnhancedFieldMappingResult;
	console.log({
		mappingType: result2.mappingType,
		confidence: result2.confidence,
		direction: `${result2.standardFieldMapping?.sourcePlatform} → ${result2.standardFieldMapping?.targetPlatform}`
	});

	// ✅ Custom field example
	const customField: GetCCCustomField = {
		id: 2,
		name: "customNote",
		label: "Custom Patient Note",
		type: "textarea",
		allowMultipleValues: false,
		validation: "",
		color: null,
		positions: [],
		useCustomSort: null,
		isRequired: false,
		allowedValues: [],
		defaultValues: []
	};

	console.log("\n📝 Custom field mapping:");
	const result3 = mapCCFieldToAPType(customField, {}) as EnhancedFieldMappingResult;
	console.log({
		mappingType: result3.mappingType,
		customFieldType: result3.customFieldType,
		confidence: result3.confidence
	});
}

/**
 * Comparison: Old vs New API
 */
export function compareOldVsNewAPI() {
	console.log("\n🔄 API Comparison: Old vs New");

	const ccField: GetCCCustomField = {
		id: 1,
		name: "email",
		label: "Email Address",
		type: "email",
		allowMultipleValues: false,
		validation: "",
		color: null,
		positions: [],
		useCustomSort: null,
		isRequired: false,
		allowedValues: [],
		defaultValues: []
	};

	// ❌ OLD WAY (redundant parameters)
	console.log("\n❌ Old way (redundant):");
	console.log(`mapCCFieldToAPType(ccField, {
		fieldName: ccField.name,        // ← Redundant!
		fieldLabel: ccField.label,      // ← Redundant!
		sourcePlatform: "cc",           // ← Could be inferred!
		targetPlatform: "ap"            // ← Could be inferred!
	})`);

	// ✅ NEW WAY (clean and simple)
	console.log("\n✅ New way (clean):");
	console.log(`mapCCFieldToAPType(ccField, {})`);
	
	const result = mapCCFieldToAPType(ccField, {}) as EnhancedFieldMappingResult;
	console.log("Result:", {
		mappingType: result.mappingType,
		targetField: result.standardFieldMapping?.targetField,
		confidence: result.confidence
	});
}

/**
 * Advanced use cases that still work
 */
export function demonstrateAdvancedUseCases() {
	console.log("\n🎯 Advanced use cases still supported:");

	const ccField: GetCCCustomField = {
		id: 1,
		name: "mobile",
		label: "Mobile",
		type: "telephone",
		allowMultipleValues: false,
		validation: "",
		color: null,
		positions: [],
		useCustomSort: null,
		isRequired: false,
		allowedValues: [],
		defaultValues: []
	};

	// Override field name for testing
	console.log("\n🔧 Override field name:");
	const result1 = mapCCFieldToAPType(ccField, {
		fieldName: "phone"  // Test with different name
	}) as EnhancedFieldMappingResult;
	console.log(`Original name: ${ccField.name}, Test name: phone, Result: ${result1.mappingType}`);

	// Override label for fuzzy matching
	console.log("\n🔧 Override label for fuzzy matching:");
	const result2 = mapCCFieldToAPType(ccField, {
		fieldLabel: "Phone Mobile"  // Better fuzzy match
	}) as EnhancedFieldMappingResult;
	console.log(`Original label: ${ccField.label}, Test label: Phone Mobile, Result: ${result2.mappingType}`);

	// Reverse direction mapping
	console.log("\n🔧 Reverse direction (AP → CC):");
	const result3 = mapCCFieldToAPType(ccField, {
		sourcePlatform: "ap",
		targetPlatform: "cc"
	}) as EnhancedFieldMappingResult;
	console.log(`Direction: AP → CC, Result: ${result3.mappingType}`);
}

// Run the examples
if (require.main === module) {
	demonstrateImprovedAPI();
	compareOldVsNewAPI();
	demonstrateAdvancedUseCases();
}
