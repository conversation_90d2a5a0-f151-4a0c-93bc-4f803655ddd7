# Custom Fields Mapping - Simple API

## TL;DR - Just use these simple functions:

```typescript
import { mapCCtoAPField, mapAPtoCCField } from "./mapping";

// CC to AP mapping - NO EXTRA PARAMETERS!
const result1 = mapCCtoAPField(ccField);

// AP to CC mapping - NO EXTRA PARAMETERS!  
const result2 = mapAPtoCCField(apField);
```

## Simple Functions

### `mapCCtoAPField(ccField)`

Maps a CliniCore field to AutoPatient with enhanced field detection.

**Parameters:**
- `ccField: GetCCCustomField` - The CC field object

**Returns:**
- `EnhancedFieldMappingResult` - Complete mapping analysis

**Example:**
```typescript
const ccField = {
  name: "phone",
  label: "Phone Number", 
  type: "telephone",
  allowMultipleValues: false,
  // ... other properties
};

const result = mapCCtoAPField(ccField);

if (result.mappingType === "standard_field") {
  console.log(`Maps to AP standard field: ${result.standardFieldMapping?.targetField}`);
} else {
  console.log(`Create AP custom field of type: ${result.customFieldType}`);
}
```

### `mapAPtoCCField(apField)`

Maps an AutoPatient field to CliniCore with enhanced field detection.

**Parameters:**
- `apField: { name: string, label?: string, type: string, allowMultipleValues: boolean }` - The AP field info

**Returns:**
- `EnhancedFieldMappingResult` - Complete mapping analysis

**Example:**
```typescript
const apField = {
  name: "email",
  label: "Email Address",
  type: "email", 
  allowMultipleValues: false
};

const result = mapAPtoCCField(apField);

if (result.mappingType === "standard_field") {
  console.log(`Maps to CC standard field: ${result.standardFieldMapping?.targetField}`);
} else {
  console.log(`Create CC custom field of type: ${result.customFieldConversion?.type}`);
}
```

## What These Functions Do

1. **Auto-extract field info** - No need to pass `fieldName` and `fieldLabel` separately
2. **Smart platform direction** - Automatically knows CC→AP or AP→CC based on function name
3. **Standard field detection** - Identifies when custom fields should map to standard fields
4. **Type conversion** - Handles field type mapping between platforms
5. **Confidence scoring** - Provides confidence levels for mappings

## Migration from Old API

### Before (fucking complicated):
```typescript
const result = mapCCFieldToAPType(ccField, {
  fieldName: ccField.name,        // ← Redundant!
  fieldLabel: ccField.label,      // ← Redundant!
  sourcePlatform: "cc",           // ← Redundant!
  targetPlatform: "ap"            // ← Redundant!
});
```

### After (dead simple):
```typescript
const result = mapCCtoAPField(ccField);
```

## Result Structure

Both functions return `EnhancedFieldMappingResult`:

```typescript
{
  mappingType: "standard_field" | "custom_field",
  confidence: number,           // 0-1 confidence score
  fieldName?: string,          // Extracted field name
  fieldLabel?: string,         // Extracted field label
  
  // For standard field mappings:
  standardFieldMapping?: {
    targetField: string,       // Target field name
    sourcePlatform: "cc" | "ap",
    targetPlatform: "cc" | "ap", 
    notes?: string
  },
  
  // For custom field conversions:
  customFieldType?: APFieldType,           // For CC→AP
  customFieldConversion?: APToCCConversionResult  // For AP→CC
}
```

## Advanced Usage

If you need the old complex API for edge cases, it's still available:

```typescript
import { mapCCFieldToAPType } from "./mapping";

// Advanced usage with overrides
const result = mapCCFieldToAPType(ccField, {
  fieldLabel: "Custom Label",    // Override label
  sourcePlatform: "ap",         // Reverse direction
  targetPlatform: "cc"
});
```

But 99% of the time, just use the simple functions!
