/**
 * Field Mapping Service
 * 
 * Practical service that uses the enhanced mapCCFieldToAPType function
 * to handle field synchronization between CC and AP platforms.
 */

import { mapCCtoAPField } from "./mapping";
import type { GetCCCustomField, APGetCustomFieldType } from "@/type";
import { logInfo, logWarn, logDebug } from "@/utils/logger";

export interface FieldMappingDecision {
	action: "map_to_standard" | "map_to_custom" | "create_custom" | "skip";
	sourceField: GetCCCustomField;
	targetField?: string; // For standard field mappings
	targetCustomField?: APGetCustomFieldType; // For custom field mappings
	confidence: number;
	reason: string;
	notes?: string;
}

/**
 * Analyze a CC field and determine the best mapping strategy for AP
 */
export function analyzeFieldMapping(
	ccField: GetCCCustomField,
	apCustomFields: APGetCustomFieldType[]
): FieldMappingDecision {
	// Use enhanced mapping to get comprehensive analysis with auto-extracted field info
	const enhancedResult = mapCCtoAPField(ccField);

	logDebug("Analyzing field mapping", {
		ccFieldName: ccField.name,
		ccFieldType: ccField.type,
		mappingType: enhancedResult.mappingType,
		confidence: enhancedResult.confidence
	});

	// Case 1: Standard field mapping detected
	if (enhancedResult.mappingType === "standard_field" && enhancedResult.standardFieldMapping) {
		return {
			action: "map_to_standard",
			sourceField: ccField,
			targetField: enhancedResult.standardFieldMapping.targetField,
			confidence: enhancedResult.confidence,
			reason: `CC custom field '${ccField.name}' maps to AP standard field '${enhancedResult.standardFieldMapping.targetField}'`,
			notes: enhancedResult.standardFieldMapping.notes
		};
	}

	// Case 2: Custom field mapping - look for existing AP custom field
	if (enhancedResult.mappingType === "custom_field" && enhancedResult.customFieldType) {
		// Try to find matching AP custom field
		const matchingAPField = findMatchingAPCustomField(ccField, apCustomFields, enhancedResult.customFieldType);
		
		if (matchingAPField) {
			return {
				action: "map_to_custom",
				sourceField: ccField,
				targetCustomField: matchingAPField,
				confidence: enhancedResult.confidence,
				reason: `CC custom field '${ccField.name}' maps to existing AP custom field '${matchingAPField.name}'`
			};
		}

		// No matching AP custom field found - suggest creation
		return {
			action: "create_custom",
			sourceField: ccField,
			confidence: enhancedResult.confidence,
			reason: `No matching AP custom field found for '${ccField.name}'. Suggest creating new ${enhancedResult.customFieldType} field.`
		};
	}

	// Fallback case
	return {
		action: "skip",
		sourceField: ccField,
		confidence: 0,
		reason: "Unable to determine appropriate mapping strategy"
	};
}

/**
 * Find matching AP custom field using enhanced type information
 */
function findMatchingAPCustomField(
	ccField: GetCCCustomField,
	apCustomFields: APGetCustomFieldType[],
	expectedType: string
): APGetCustomFieldType | null {
	return apCustomFields.find(apField => {
		// Type must match
		if (apField.dataType !== expectedType) return false;

		// Name matching (using simple string comparison for now)
		const nameMatch = apField.name.toLowerCase() === ccField.name.toLowerCase() ||
						  apField.name.toLowerCase() === ccField.label.toLowerCase();

		// Field key matching (if available)
		const keyMatch = apField.fieldKey?.split('.').pop()?.toLowerCase() === ccField.name.toLowerCase();

		return nameMatch || keyMatch;
	}) || null;
}

/**
 * Process multiple CC fields and generate mapping decisions
 */
export function generateMappingPlan(
	ccFields: GetCCCustomField[],
	apCustomFields: APGetCustomFieldType[]
): {
	decisions: FieldMappingDecision[];
	summary: {
		standardMappings: number;
		customMappings: number;
		newFieldsNeeded: number;
		skipped: number;
	};
} {
	const decisions = ccFields.map(ccField => analyzeFieldMapping(ccField, apCustomFields));

	const summary = {
		standardMappings: decisions.filter(d => d.action === "map_to_standard").length,
		customMappings: decisions.filter(d => d.action === "map_to_custom").length,
		newFieldsNeeded: decisions.filter(d => d.action === "create_custom").length,
		skipped: decisions.filter(d => d.action === "skip").length
	};

	logInfo("Field mapping plan generated", {
		totalFields: ccFields.length,
		...summary
	});

	return { decisions, summary };
}

/**
 * Execute field mapping decisions (example implementation)
 */
export async function executeMappingPlan(decisions: FieldMappingDecision[]): Promise<void> {
	for (const decision of decisions) {
		switch (decision.action) {
			case "map_to_standard":
				logInfo(`✅ Standard field mapping: ${decision.sourceField.name} → ${decision.targetField}`);
				// TODO: Implement standard field value sync
				break;

			case "map_to_custom":
				logInfo(`🔗 Custom field mapping: ${decision.sourceField.name} → ${decision.targetCustomField?.name}`);
				// TODO: Implement custom field value sync
				break;

			case "create_custom":
				logWarn(`🆕 Need to create AP custom field for: ${decision.sourceField.name}`);
				// TODO: Implement AP custom field creation
				break;

			case "skip":
				logWarn(`⏭️ Skipping field: ${decision.sourceField.name} - ${decision.reason}`);
				break;
		}
	}
}
