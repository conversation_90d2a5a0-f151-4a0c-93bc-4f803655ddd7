import apiClient from "@/apiClient";
import type { APGetCustomFieldType, GetCCCustomField } from "@/type";
import matchString from "@/utils/matchString";
import { getBidirectionalMapping, mapCCFieldToAPType } from "./mapping";
/**
 * Custom Fields Processor
 *
 * Simple processor that returns empty JSON response.
 */

export async function synchronizeCustomFields(): Promise<{}> {
	const apCustomFields = await apiClient.ap.apCustomfield.allWithParentFilter();
	const ccCustomFields = await apiClient.cc.ccCustomfieldReq.all();

	const matchFields = [];

	ccCustomFields.forEach((ccf) => {
		findAPField(ccf, apCustomFields);
	});

	return {
		apCustomFields: apCustomFields.map((f) => f.name),
		// ccCustomFields,
	};
}

const findAPField = (
	ccField: GetCCCustomField,
	apCustomFields: APGetCustomFieldType[],
) => {
	// Use enhanced mapping to check for standard field mappings first
	const enhancedResult = mapCCFieldToAPType(ccField, {});

	// If it's a standard field mapping, handle it differently
	if (enhancedResult.mappingType === "standard_field") {
		console.log("🎯 STANDARD FIELD MAPPING DETECTED:", {
			ccFieldName: ccField.name,
			ccFieldLabel: ccField.label,
			apStandardField: enhancedResult.standardFieldMapping?.targetField,
			confidence: enhancedResult.confidence,
			notes: enhancedResult.standardFieldMapping?.notes
		});
		// Don't try to match with custom fields - this should map to AP standard field
		return {
			type: "standard_field_mapping",
			targetField: enhancedResult.standardFieldMapping?.targetField,
			confidence: enhancedResult.confidence
		};
	}

	// For custom field mappings, use the existing logic with enhanced type info
	const customFieldType = enhancedResult.customFieldType;
	const apField = apCustomFields.find((apf) => {
		const apName = matchString(apf.name, ccField.name);
		const apLabel = matchString(apf.name, ccField.label);
		const keyword = matchString(apf.fieldKey?.split(".")?.pop(), ccField.name);
		const apType = customFieldType === apf.dataType;
		return (apName || apLabel || keyword) && apType;
	});

	if (!apField) {
		const { name, label, allowMultipleValues, allowedValues, ...restCC } =
			ccField;
		console.log("🔍 NO AP CUSTOM FIELD MATCH FOUND:", {
			ccField: {
				name,
				label,
				allowMultipleValues,
				allowedValues: JSON.stringify(allowedValues.map((v) => v.value)),
				type: ccField.type,
			},
			suggestedAPType: customFieldType,
			confidence: enhancedResult.confidence
		});
		console.log("-------------------------");
		return {
			type: "no_match",
			suggestedAPType: customFieldType,
			ccField: { name, label, type: ccField.type, allowMultipleValues }
		};
	}

	// console.log("✅ CUSTOM FIELD MATCH FOUND:", {
	// 	ccFieldName: ccField.name,
	// 	apFieldName: apField.name,
	// 	fieldType: customFieldType
	// });

	return {
		type: "custom_field_match",
		apField,
		ccField,
		fieldType: customFieldType
	};
};
